'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { authenticatedFetch } from '../../../lib/auth';
import DashboardLayout, { DashboardHeader } from '../../../components/dashboard/DashboardLayout';

interface Plan {
  id: string;
  name: string;
  description?: string;
  features: {
    maxInvoices: number;
    maxStorage: number;
    documentAnalysis: boolean;
    prioritySupport: boolean;
    apiAccess: boolean;
    customBranding: boolean;
    multiUser: boolean;
    maxUsers: number;
    advancedReports: boolean;
  };
  pricing: {
    monthly: number;
    yearly: number;
  };
  popular?: boolean;
}

interface Subscription {
  planId: string;
  planName: string;
  status: string;
  currentPeriodEnd: string;
  amount: number;
  interval: string;
  features: any;
  usage: any;
  usagePercentages: any;
  isNearLimit: boolean;
}

export default function SubscriptionPage() {
  const { user } = useAuth();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [error, setError] = useState('');

  // Test popup blocker
  const testPopup = () => {
    const popup = window.open('', '_blank', 'width=500,height=600');
    if (!popup || popup.closed || typeof popup.closed === 'undefined') {
      setError('Popup blocker is enabled. Please disable it and try again.');
    } else {
      popup.close();
      setError('Popup test successful! Popup blocker is not blocking.');
    }
  };

  useEffect(() => {
    if (user) {
      fetchPlansAndSubscription();
    }
  }, [user]);

  const fetchPlansAndSubscription = async () => {
    try {
      // First sync usage to ensure accurate data
      try {
        await authenticatedFetch('/api/subscriptions/usage/sync', {
          method: 'POST'
        });
      } catch (syncError) {
        // Continue even if sync fails
      }

      const [plansResponse, subscriptionResponse] = await Promise.all([
        authenticatedFetch('/api/subscriptions/plans'),
        authenticatedFetch('/api/subscriptions/current')
      ]);

      const plansData = await plansResponse.json();
      const subscriptionData = await subscriptionResponse.json();

      if (plansData.success) {
        setPlans(plansData.data.plans);
      }

      if (subscriptionData.success) {
        setCurrentSubscription(subscriptionData.data.subscription);
      }
    } catch (error) {
      setError('Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };







  const handleUpgrade = async (planId: string) => {
    console.log('🚀 Starting upgrade process for plan:', planId);

    if (planId === 'free') {
      // Handle downgrade to free
      try {
        setUpgrading(planId);
        const response = await authenticatedFetch('/api/subscriptions/change', {
          method: 'POST',
          body: JSON.stringify({ planId, interval: billingInterval })
        });

        const data = await response.json();
        if (data.success) {
          await fetchPlansAndSubscription();
          setError('');
        } else {
          setError(data.message);
        }
      } catch (error) {
        setError('Failed to change subscription');
      } finally {
        setUpgrading(null);
      }
      return;
    }

    // Handle paid plan upgrade
    try {
      setUpgrading(planId);
      setError(''); // Clear any previous errors

      console.log('💳 Creating payment order...');
      // Create payment order
      const orderResponse = await authenticatedFetch('/api/subscriptions/payment/create-order', {
        method: 'POST',
        body: JSON.stringify({ planId, interval: billingInterval })
      });

      console.log('📡 Order response status:', orderResponse.status);
      const orderData = await orderResponse.json();
      console.log('📋 Order data:', orderData);

      if (!orderData.success) {
        console.error('❌ Order creation failed:', orderData.message);
        throw new Error(orderData.message || 'Failed to create payment order');
      }

      // Check if Razorpay key is available
      if (!orderData.data.key) {
        console.error('❌ Razorpay key not found in response');
        throw new Error('Payment gateway is not configured. Please contact support.');
      }

      console.log('✅ Order created successfully, initializing Razorpay...');

      // Initialize Razorpay payment
      const options = {
        key: orderData.data.key,
        amount: orderData.data.amount,
        currency: orderData.data.currency,
        name: 'InvoNest',
        description: `Upgrade to ${plans.find(p => p.id === planId)?.name} Plan`,
        order_id: orderData.data.orderId,
        handler: async (response: any) => {
          try {
            // Verify payment
            const verifyResponse = await authenticatedFetch('/api/subscriptions/payment/verify', {
              method: 'POST',
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature
              })
            });

            const verifyData = await verifyResponse.json();
            if (verifyData.success) {
              await fetchPlansAndSubscription();
              setError('');
              alert('Subscription upgraded successfully!');
            } else {
              throw new Error(verifyData.message || 'Payment verification failed');
            }
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Payment verification failed');
          } finally {
            setUpgrading(null);
          }
        },
        modal: {
          ondismiss: () => {
            setUpgrading(null);
          },
          // Add browser compatibility options
          escape: true,
          animation: true,
          confirm_close: false
        },
        theme: {
          color: '#4F46E5'
        },
        // Add browser compatibility configuration
        config: {
          display: {
            blocks: {
              banks: {
                name: 'Complete Payment',
                instruments: [
                  {
                    method: 'card'
                  },
                  {
                    method: 'netbanking'
                  },
                  {
                    method: 'wallet'
                  },
                  {
                    method: 'upi'
                  }
                ]
              }
            },
            sequence: ['block.banks'],
            preferences: {
              show_default_blocks: true
            }
          }
        },
        // Add retry configuration
        retry: {
          enabled: true,
          max_count: 3
        }
      };

      // Check if Razorpay script is already loaded
      if ((window as any).Razorpay) {
        console.log('✅ Razorpay script already loaded, opening payment gateway...');
        try {
          const rzp = new (window as any).Razorpay(options);

          rzp.on('payment.failed', function (response: any) {
            console.error('❌ Payment failed:', response.error);
            setError(`Payment failed: ${response.error.description}`);
            setUpgrading(null);
          });

          rzp.open();
          console.log('🎉 Razorpay payment gateway opened successfully');

          // Clear loading state after a short delay if popup doesn't open
          setTimeout(() => {
            if (upgrading === planId) {
              console.log('⚠️ Popup may be blocked - clearing loading state');
              setUpgrading(null);
              setError('Payment popup may be blocked. Please allow popups and try again.');
            }
          }, 3000);
        } catch (error) {
          console.error('❌ Failed to initialize Razorpay:', error);
          setError('Failed to initialize payment gateway');
          setUpgrading(null);
        }
        return;
      }

      console.log('📦 Loading Razorpay script...');
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        console.log('✅ Razorpay script loaded successfully');
        try {
          const rzp = new (window as any).Razorpay(options);

          rzp.on('payment.failed', function (response: any) {
            console.error('❌ Payment failed:', response.error);
            setError(`Payment failed: ${response.error.description}`);
            setUpgrading(null);
          });

          rzp.open();
          console.log('🎉 Razorpay payment gateway opened successfully');

          // Clear loading state after a short delay if popup doesn't open
          setTimeout(() => {
            if (upgrading === planId) {
              console.log('⚠️ Popup may be blocked - clearing loading state');
              setUpgrading(null);
              setError('Payment popup may be blocked. Please allow popups and try again.');
            }
          }, 3000);
        } catch (error) {
          console.error('❌ Failed to initialize Razorpay after script load:', error);
          setError('Failed to initialize payment gateway');
          setUpgrading(null);
        }
      };
      script.onerror = () => {
        console.error('❌ Failed to load Razorpay script');
        setError('Failed to load payment gateway');
        setUpgrading(null);
      };
      document.head.appendChild(script);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to upgrade subscription');
      setUpgrading(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(price / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <svg className="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-lg text-gray-600">Loading subscription...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <DashboardHeader 
        title="Subscription & Billing"
        subtitle="Manage your InvoNest subscription and view usage"
      />

      <div className="max-w-7xl mx-auto p-4 lg:p-8 space-y-8">
        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        {/* Popup Test Button (for debugging) */}
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-md">
          <p className="text-blue-800 mb-2">
            If payment popup is not opening, test if popup blocker is enabled:
          </p>
          <button
            onClick={testPopup}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Test Popup Blocker
          </button>
        </div>



        {/* Current Subscription */}
        {currentSubscription && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Current Subscription</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{currentSubscription.planName}</h3>
                <p className="text-gray-600">
                  {formatPrice(currentSubscription.amount)} / {currentSubscription.interval}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  {currentSubscription.status === 'active' ? 'Active' : currentSubscription.status}
                  {currentSubscription.currentPeriodEnd && (
                    <> until {formatDate(currentSubscription.currentPeriodEnd)}</>
                  )}
                </p>
              </div>

              {/* Usage Overview */}
              <div className="md:col-span-2">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Usage This Period</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm text-gray-900">
                      <span>Invoices</span>
                      <span>
                        {currentSubscription.usage.invoicesUsed} / {currentSubscription.features.maxInvoices === -1 ? '∞' : currentSubscription.features.maxInvoices}
                      </span>
                    </div>
                    {currentSubscription.features.maxInvoices !== -1 && (
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className={`h-2 rounded-full ${getUsageColor(currentSubscription.usagePercentages.invoices).split(' ')[1]}`}
                          style={{ width: `${Math.min(currentSubscription.usagePercentages.invoices, 100)}%` }}
                        ></div>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="flex justify-between text-sm text-gray-900">
                      <span>Storage</span>
                      <span>
                        {currentSubscription.usage.storageUsed}MB / {currentSubscription.features.maxStorage}MB
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className={`h-2 rounded-full ${getUsageColor(currentSubscription.usagePercentages.storage).split(' ')[1]}`}
                        style={{ width: `${Math.min(currentSubscription.usagePercentages.storage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Billing Toggle */}
        <div className="flex justify-center">
          <div className="bg-gray-100 rounded-lg p-1 flex">
            <button
              onClick={() => setBillingInterval('monthly')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                billingInterval === 'monthly'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-800 hover:text-gray-900'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingInterval('yearly')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                billingInterval === 'yearly'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-800 hover:text-gray-900'
              }`}
            >
              Yearly
              <span className="ml-1 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded">
                Save 17%
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-xl shadow-sm border-2 transition-all ${
                plan.popular
                  ? 'border-indigo-500 ring-2 ring-indigo-200'
                  : 'border-gray-200 hover:border-indigo-300'
              } ${currentSubscription?.planId === plan.id ? 'ring-2 ring-green-200 border-green-500' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-full">
                    Most Popular
                  </span>
                </div>
              )}

              {currentSubscription?.planId === plan.id && (
                <div className="absolute -top-3 right-4">
                  <span className="bg-green-600 text-white px-3 py-1 text-xs font-medium rounded-full">
                    Current Plan
                  </span>
                </div>
              )}

              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
                <p className="text-gray-600 mt-2">{plan.description}</p>

                <div className="mt-4">
                  <span className="text-3xl font-bold text-gray-900">
                    {formatPrice(plan.pricing[billingInterval])}
                  </span>
                  {plan.pricing[billingInterval] > 0 && (
                    <span className="text-gray-600 ml-1">
                      /{billingInterval === 'monthly' ? 'month' : 'year'}
                    </span>
                  )}
                </div>

                {billingInterval === 'yearly' && plan.pricing.yearly > 0 && (
                  <p className="text-sm text-green-600 mt-1">
                    Save ₹{((plan.pricing.monthly * 12 - plan.pricing.yearly) / 100).toLocaleString('en-IN')} per year
                  </p>
                )}

                <ul className="mt-6 space-y-3">
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-sm text-gray-900">
                      {plan.features.maxInvoices === -1 ? 'Unlimited' : plan.features.maxInvoices} invoices/month
                    </span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-sm text-gray-900">
                      {plan.features.maxStorage}MB storage
                    </span>
                  </li>
                  {plan.features.documentAnalysis && (
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-gray-900">Document analysis</span>
                    </li>
                  )}
                  {plan.features.prioritySupport && (
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-gray-900">Priority support</span>
                    </li>
                  )}
                  {plan.features.apiAccess && (
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-gray-900">API access</span>
                    </li>
                  )}
                  {plan.features.multiUser && (
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-gray-900">
                        Up to {plan.features.maxUsers} users
                      </span>
                    </li>
                  )}
                </ul>

                <button
                  onClick={() => handleUpgrade(plan.id)}
                  disabled={upgrading === plan.id || currentSubscription?.planId === plan.id}
                  className={`w-full mt-6 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    currentSubscription?.planId === plan.id
                      ? 'bg-green-100 text-green-800 cursor-not-allowed'
                      : plan.popular
                      ? 'bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200 disabled:opacity-50'
                  }`}
                >
                  {upgrading === plan.id ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </div>
                  ) : currentSubscription?.planId === plan.id ? (
                    'Current Plan'
                  ) : plan.id === 'free' ? (
                    'Downgrade to Free'
                  ) : (
                    `Upgrade to ${plan.name}`
                  )}
                </button>


              </div>
            </div>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Feature Comparison</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Feature</th>
                  {plans.map(plan => (
                    <th key={plan.id} className="text-center py-3 px-4 font-medium text-gray-900">
                      {plan.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                <tr>
                  <td className="py-3 px-4 text-sm text-gray-900">Monthly Invoices</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="text-center py-3 px-4 text-sm text-gray-900">
                      {plan.features.maxInvoices === -1 ? '∞' : plan.features.maxInvoices}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="py-3 px-4 text-sm text-gray-900">Storage</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="text-center py-3 px-4 text-sm text-gray-900">
                      {plan.features.maxStorage}MB
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="py-3 px-4 text-sm text-gray-900">Document Analysis</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="text-center py-3 px-4">
                      {plan.features.documentAnalysis ? (
                        <svg className="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="py-3 px-4 text-sm text-gray-900">Priority Support</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="text-center py-3 px-4">
                      {plan.features.prioritySupport ? (
                        <svg className="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="py-3 px-4 text-sm text-gray-900">API Access</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="text-center py-3 px-4">
                      {plan.features.apiAccess ? (
                        <svg className="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

