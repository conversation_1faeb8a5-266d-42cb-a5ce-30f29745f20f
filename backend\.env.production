# Production Environment Configuration for Render
# Copy these values to Render Environment Variables

# Server Configuration
NODE_ENV=production
PORT=5000
FRONTEND_URL=https://invonest.vercel.app

# Database Configuration
MONGODB_URI=mongodb+srv://guptaa1289:<EMAIL>/invonest

# JWT Configuration
JWT_SECRET=invonest-super-secret-jwt-key-2024-production-32chars
JWT_REFRESH_SECRET=invonest-refresh-secret-jwt-key-2024-production-32chars
JWT_EXPIRE=7d

# Razorpay Payment Gateway
RAZORPAY_KEY_ID=rzp_test_FZjGGkxRPPju8I
RAZORPAY_KEY_SECRET=I1OgjfBYbkzjzO4kHGI28jOQ

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=aery myha nqrl vaex

# reCAPTCHA Configuration
RECAPTCHA_SECRET_KEY=6LfjK4grAAAAAB2GkZptDqpOV8T_hiausJHMu5J5
RECAPTCHA_MIN_SCORE=0.5

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_MAX=100

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456
ADMIN_NAME=InvoNest Admin

# Blockchain Configuration
POLYGON_RPC_URL=https://rpc-mumbai.maticvigil.com
PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000
